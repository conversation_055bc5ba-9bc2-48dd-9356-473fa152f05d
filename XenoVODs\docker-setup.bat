@echo off
echo Setting up configuration files for Docker...

REM Create config directory if it doesn't exist
if not exist config mkdir config

REM Copy template files if they don't exist
if not exist config\config.json (
  echo Creating config\config.json from template...
  copy config\template_config.json config\config.json
  echo Please edit config\config.json with your API credentials
)

if not exist config\default.json (
  echo Creating config\default.json from template...
  copy config\template_default.json config\default.json
  echo Please edit config\default.json with your settings
)

if not exist config\production.json (
  echo Creating config\production.json from template...
  copy config\template_default.json config\production.json
  echo Please edit config\production.json with your production settings
)

echo Configuration files have been set up.
echo You can now run 'docker-compose up -d' to start the application.
