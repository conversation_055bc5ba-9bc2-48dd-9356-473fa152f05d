const axios = require('axios');

class ConfigClient {
    constructor(configServerUrl = 'http://localhost:3001') {
        this.baseUrl = configServerUrl;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
    }

    /**
     * Get all playlist links from config server
     * @returns {Promise<Object>} Object with playlist links (game name -> playlist URL)
     */
    async getAllPlaylists() {
        const cacheKey = 'all_playlists';

        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const response = await axios.get(`${this.baseUrl}/api/playlist-links`, {
                timeout: 5000
            });

            const data = response.data;

            // Cache the result
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            console.error('Error fetching playlist links:', error.message);

            // Return cached data if available, even if expired
            if (this.cache.has(cacheKey)) {
                console.log('Returning cached data due to error');
                return this.cache.get(cacheKey).data;
            }

            throw new Error(`Failed to fetch playlist links: ${error.message}`);
        }
    }

    /**
     * Get playlist link for specific game
     * @param {string} gameName - Name of the game
     * @returns {Promise<string|null>} Playlist URL for the game or null if not found
     */
    async getPlaylistForGame(gameName) {
        try {
            const allPlaylists = await this.getAllPlaylists();
            return allPlaylists.playlists[gameName] || null;
        } catch (error) {
            console.error(`Error fetching playlist for ${gameName}:`, error.message);
            throw new Error(`Failed to fetch playlist for ${gameName}: ${error.message}`);
        }
    }

    /**
     * Get simple mapping of game names to playlist URLs
     * @returns {Promise<Object>} Simple mapping object (game name -> playlist URL)
     */
    async getPlaylistMapping() {
        try {
            const allPlaylists = await this.getAllPlaylists();
            return allPlaylists.playlists || {};
        } catch (error) {
            console.error('Error fetching playlist mapping:', error.message);
            throw new Error(`Failed to fetch playlist mapping: ${error.message}`);
        }
    }

    /**
     * Check if config server is healthy
     * @returns {Promise<boolean>} True if server is healthy
     */
    async isHealthy() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/health`, {
                timeout: 3000
            });
            return response.data.status === 'ok';
        } catch (error) {
            console.error('Config server health check failed:', error.message);
            return false;
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get cache stats
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            timeout: this.cacheTimeout
        };
    }

    /**
     * Sync playlist links with XennyGames database
     * @param {Object} gameData - Current game data from XennyGames
     * @returns {Promise<Object>} Updated game data with playlist links
     */
    async syncPlaylistsWithGames(gameData) {
        try {
            const playlists = await this.getPlaylistMapping();

            let updatedCount = 0;
            const updatedGames = { ...gameData.games };

            // Update games with playlist links
            for (const [gameName, gameInfo] of Object.entries(updatedGames)) {
                if (playlists[gameName]) {
                    const playlistUrl = playlists[gameName];

                    if ((!gameInfo.youtube_playlist || gameInfo.youtube_playlist !== playlistUrl) && 
                        gameInfo.youtube_playlist_source !== 'manual' ()) {
                        const updatedGame = {
                            ...gameInfo,
                            youtube_playlist: playlistUrl,
                            youtube_playlist_source: 'config_server',
                            youtube_playlist_updated: new Date().toISOString()
                        };

                        // Remove any existing youtube_playlist_info field for consistency
                        if (updatedGame.youtube_playlist_info) {
                            delete updatedGame.youtube_playlist_info;
                        }

                        updatedGames[gameName] = updatedGame;
                        updatedCount++;
                    }
                }
            }

            return {
                games: updatedGames,
                votes: gameData.votes,
                syncStats: {
                    totalGames: Object.keys(updatedGames).length,
                    playlistsAvailable: Object.keys(playlists).length,
                    gamesUpdated: updatedCount,
                    lastSync: new Date().toISOString()
                }
            };
        } catch (error) {
            console.error('Error syncing playlist links with games:', error);
            throw error;
        }
    }
}

module.exports = ConfigClient;
