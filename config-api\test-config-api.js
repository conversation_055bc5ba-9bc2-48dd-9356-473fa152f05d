const axios = require('axios');
const ConfigClient = require('./config-client');

const CONFIG_SERVER_URL = 'http://localhost:3001';

async function testConfigAPI() {
    console.log('Testing Config API Server...\n');

    try {
        // Test 1: Health check
        console.log('1. Testing health endpoint...');
        const healthResponse = await axios.get(`${CONFIG_SERVER_URL}/api/health`);
        console.log('✅ Health check:', healthResponse.data);
        console.log('');

        // Test 2: Get all playlist links
        console.log('2. Testing get all playlist links...');
        const playlistsResponse = await axios.get(`${CONFIG_SERVER_URL}/api/playlist-links`);
        console.log('✅ All playlist links:', {
            success: playlistsResponse.data.success,
            count: playlistsResponse.data.count,
            samplePlaylists: Object.keys(playlistsResponse.data.playlists || {}).slice(0, 3)
        });
        console.log('');

        // Test 3: Get specific playlist link
        console.log('3. Testing get specific playlist link...');
        const playlists = playlistsResponse.data.playlists || {};
        const firstGameName = Object.keys(playlists)[0];

        if (firstGameName) {
            const specificResponse = await axios.get(`${CONFIG_SERVER_URL}/api/playlist-links/${encodeURIComponent(firstGameName)}`);
            console.log('✅ Specific playlist link:', {
                gameName: firstGameName,
                success: specificResponse.data.success,
                hasPlaylistUrl: !!specificResponse.data.playlistUrl
            });
        } else {
            console.log('⚠️ No playlist links found to test specific endpoint');
        }
        console.log('');

        // Test 4: Get playlist mapping
        console.log('4. Testing playlist mapping...');
        const mappingResponse = await axios.get(`${CONFIG_SERVER_URL}/api/playlist-mapping`);
        console.log('✅ Playlist mapping:', {
            success: mappingResponse.data.success,
            count: mappingResponse.data.count,
            sampleMapping: Object.entries(mappingResponse.data.mapping || {}).slice(0, 2)
        });
        console.log('');

        // Test 5: Config stats
        console.log('5. Testing config stats...');
        const statsResponse = await axios.get(`${CONFIG_SERVER_URL}/api/config/stats`);
        console.log('✅ Config stats:', {
            success: statsResponse.data.success,
            playlistCount: statsResponse.data.stats?.playlistCount,
            fileSize: statsResponse.data.stats?.size
        });
        console.log('');

    } catch (error) {
        console.error('❌ API Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

async function testConfigClient() {
    console.log('\nTesting Config Client...\n');

    const client = new ConfigClient(CONFIG_SERVER_URL);

    try {
        // Test 1: Health check
        console.log('1. Testing client health check...');
        const isHealthy = await client.isHealthy();
        console.log('✅ Client health check:', isHealthy);
        console.log('');

        // Test 2: Get all playlist links
        console.log('2. Testing client get all playlist links...');
        const allPlaylists = await client.getAllPlaylists();
        console.log('✅ Client all playlist links:', {
            success: allPlaylists.success,
            count: allPlaylists.count
        });
        console.log('');

        // Test 3: Get playlist mapping
        console.log('3. Testing client playlist mapping...');
        const mapping = await client.getPlaylistMapping();
        console.log('✅ Client playlist mapping:', {
            count: Object.keys(mapping).length,
            sampleMappings: Object.entries(mapping).slice(0, 2)
        });
        console.log('');

        // Test 4: Cache stats
        console.log('4. Testing client cache...');
        const cacheStats = client.getCacheStats();
        console.log('✅ Client cache stats:', cacheStats);
        console.log('');

        // Test 5: Sync with mock game data
        console.log('5. Testing sync with mock game data...');
        const mockGameData = {
            games: {
                'Ghost of Tsushima': {
                    name: 'Ghost of Tsushima',
                    owned: true,
                    overlay: 'Gespielt'
                },
                'Test Game': {
                    name: 'Test Game',
                    owned: false,
                    overlay: 'Auf der Spieleliste'
                }
            },
            votes: {}
        };

        const syncedData = await client.syncPlaylistsWithGames(mockGameData);
        console.log('✅ Client sync test:', {
            totalGames: syncedData.syncStats.totalGames,
            gamesUpdated: syncedData.syncStats.gamesUpdated,
            playlistsAvailable: syncedData.syncStats.playlistsAvailable
        });
        console.log('');

    } catch (error) {
        console.error('❌ Client test failed:', error.message);
    }
}

async function runTests() {
    console.log('='.repeat(50));
    console.log('CONFIG API SERVER TESTS');
    console.log('='.repeat(50));
    
    // Check if server is running
    try {
        await axios.get(`${CONFIG_SERVER_URL}/api/health`, { timeout: 2000 });
        console.log('✅ Config server is running\n');
    } catch (error) {
        console.error('❌ Config server is not running!');
        console.error('Please start the config server first:');
        console.error('node config-api-server.js\n');
        return;
    }

    await testConfigAPI();
    await testConfigClient();

    console.log('='.repeat(50));
    console.log('TESTS COMPLETED');
    console.log('='.repeat(50));
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { testConfigAPI, testConfigClient, runTests };
