const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.CONFIG_API_PORT || 3001;

// Enable CORS for cross-origin requests from XennyGames server
app.use(cors());
app.use(express.json());

// Path to the config file
const CONFIG_PATH = path.join(__dirname, '..', 'XenoVODs', 'config', 'config.json');

// Cache for config data to avoid reading file on every request
let configCache = null;
let lastModified = null;

// Function to load config from file
function loadConfig() {
    try {
        const stats = fs.statSync(CONFIG_PATH);
        const currentModified = stats.mtime.getTime();
        
        // Only reload if file has been modified
        if (!configCache || !lastModified || currentModified > lastModified) {
            console.log('Loading config from file...');
            const configData = fs.readFileSync(CONFIG_PATH, 'utf8');
            configCache = JSON.parse(configData);
            lastModified = currentModified;
        }
        
        return configCache;
    } catch (error) {
        console.error('Error loading config:', error);
        return null;
    }
}

// Function to extract playlist links (simple mapping: game name -> playlist URL)
function extractPlaylistLinks(config) {
    if (!config || !config.youtube || !config.youtube.playlistsByChapter) {
        return {};
    }

    const playlists = config.youtube.playlistsByChapter;
    const result = {};

    // Convert playlist IDs to full URLs
    for (const [gameName, playlistId] of Object.entries(playlists)) {
        result[gameName] = `https://www.youtube.com/playlist?list=${playlistId}`;
    }

    return result;
}

// API Endpoints

// Get all playlist links (simplified)
app.get('/api/playlist-links', (req, res) => {
    try {
        const config = loadConfig();
        if (!config) {
            return res.status(500).json({ error: 'Failed to load config' });
        }

        const playlists = extractPlaylistLinks(config);

        res.json({
            success: true,
            count: Object.keys(playlists).length,
            playlists: playlists,
            lastUpdated: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting playlist links:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get playlist link for specific game
app.get('/api/playlist-links/:gameName', (req, res) => {
    try {
        const { gameName } = req.params;
        const config = loadConfig();

        if (!config) {
            return res.status(500).json({ error: 'Failed to load config' });
        }

        const playlists = extractPlaylistLinks(config);
        const playlistUrl = playlists[gameName];

        if (!playlistUrl) {
            return res.status(404).json({
                error: 'Playlist not found',
                gameName: gameName
            });
        }

        res.json({
            success: true,
            gameName: gameName,
            playlistUrl: playlistUrl
        });
    } catch (error) {
        console.error('Error getting playlist link:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get playlist mapping (game name -> playlist URL) - simplified format
app.get('/api/playlist-mapping', (req, res) => {
    try {
        const config = loadConfig();
        if (!config) {
            return res.status(500).json({ error: 'Failed to load config' });
        }

        const mapping = extractPlaylistLinks(config);

        res.json({
            success: true,
            mapping: mapping,
            count: Object.keys(mapping).length,
            lastUpdated: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting playlist mapping:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    const config = loadConfig();
    res.json({
        status: 'ok',
        configLoaded: !!config,
        configPath: CONFIG_PATH,
        configExists: fs.existsSync(CONFIG_PATH),
        timestamp: new Date().toISOString()
    });
});

// Get config file stats
app.get('/api/config/stats', (req, res) => {
    try {
        if (!fs.existsSync(CONFIG_PATH)) {
            return res.status(404).json({ error: 'Config file not found' });
        }
        
        const stats = fs.statSync(CONFIG_PATH);
        const config = loadConfig();
        
        res.json({
            success: true,
            stats: {
                size: stats.size,
                modified: stats.mtime,
                created: stats.birthtime,
                playlistCount: config?.youtube?.playlistsByChapter ? 
                    Object.keys(config.youtube.playlistsByChapter).length : 0
            }
        });
    } catch (error) {
        console.error('Error getting config stats:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
    console.log(`Config API Server running on port ${PORT}`);
    console.log(`Config path: ${CONFIG_PATH}`);
    console.log(`Config exists: ${fs.existsSync(CONFIG_PATH)}`);
    
    // Load config on startup to verify it works
    const config = loadConfig();
    if (config) {
        const playlistCount = config.youtube?.playlistsByChapter ?
            Object.keys(config.youtube.playlistsByChapter).length : 0;
        console.log(`Loaded config with ${playlistCount} playlist links`);
    } else {
        console.warn('Failed to load config on startup');
    }
});

module.exports = app;
