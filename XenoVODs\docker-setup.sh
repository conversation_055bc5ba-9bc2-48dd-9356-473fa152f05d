#!/bin/bash

# Create config directory if it doesn't exist
mkdir -p config

# Copy template files if they don't exist
if [ ! -f config/config.json ]; then
  echo "Creating config/config.json from template..."
  cp config/template_config.json config/config.json
  echo "Please edit config/config.json with your API credentials"
fi

if [ ! -f config/default.json ]; then
  echo "Creating config/default.json from template..."
  cp config/template_default.json config/default.json
  echo "Please edit config/default.json with your settings"
fi

if [ ! -f config/production.json ]; then
  echo "Creating config/production.json from template..."
  cp config/template_default.json config/production.json
  echo "Please edit config/production.json with your production settings"
fi

echo "Configuration files have been set up."
echo "You can now run 'docker-compose up -d' to start the application."
