// See https://sequelize.org/master/manual/model-basics.html
// for more of what you can do here.
const Sequelize = require("sequelize");
const DataTypes = Sequelize.DataTypes;

module.exports = function (app) {
  const sequelizeClient = app.get("sequelizeClient");
  const episodes = sequelizeClient.define(
    "episodes",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
      },
      game_name: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
      },
      episode_count: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
      },
    },
    {
      hooks: {
        beforeCount(options) {
          options.raw = true;
        },
      },
    }
  );

  return episodes;
};