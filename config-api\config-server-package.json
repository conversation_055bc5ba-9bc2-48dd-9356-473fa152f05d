{"name": "xennygames-config-api", "version": "1.0.0", "description": "API server to expose XenoVODs config playlists to XennyGames", "main": "config-api-server.js", "scripts": {"start": "node config-api-server.js", "dev": "nodemon config-api-server.js", "test": "node test-config-api.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["xennygames", "config", "api", "playlists", "youtube"], "author": "<PERSON>ennyGames", "license": "MIT"}