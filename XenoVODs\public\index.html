<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOD Archive Config Editor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --accent-color: #007bff;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .config-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            margin-bottom: 25px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .config-section:hover {
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            transform: translateY(-2px);
        }

        .config-section.primary {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #2a4a6b 100%);
            border: 2px solid var(--accent-color);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
        }

        .section-title {
            color: var(--text-primary);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 15px;
            margin-bottom: 25px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .section-title.primary {
            color: #4fc3f7;
            font-size: 1.6rem;
            text-shadow: 0 0 10px rgba(79, 195, 247, 0.3);
        }

        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .array-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .array-item input {
            flex: 1;
            margin-right: 12px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
        }

        .array-item input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }

        .btn-remove {
            background: var(--danger-color);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-remove:hover {
            background: #c82333;
            transform: scale(1.05);
        }

        .btn-add {
            background: var(--success-color);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 15px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-add:hover {
            background: #218838;
            transform: scale(1.05);
        }

        .playlist-item, .template-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            gap: 12px;
            padding: 10px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .playlist-item input, .template-item input {
            flex: 1;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
        }

        .playlist-item input:focus, .template-item input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }

        .form-control {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .form-control:focus {
            background: var(--bg-tertiary);
            border-color: var(--accent-color);
            color: var(--text-primary);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }

        .form-label {
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-check-input:checked {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .form-check-label {
            color: var(--text-secondary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-color) 0%, #0056b3 100%);
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .collapse-toggle {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .collapse-toggle:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .collapse-toggle i {
            transition: transform 0.3s ease;
        }

        .collapse-toggle[aria-expanded="true"] i {
            transform: rotate(180deg);
        }

        .alert-info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
            color: #4fc3f7;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #28a745;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }

        h1 {
            color: var(--text-primary);
            text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
        }

        .priority-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
            text-shadow: none;
        }

        .preview-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .preview-card:hover {
            border-color: var(--accent-color);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        }

        .preview-title {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 10px 15px;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            color: var(--text-primary);
            margin: 10px 0;
            word-break: break-all;
        }

        .preview-template {
            background: var(--bg-secondary);
            border-left: 3px solid var(--accent-color);
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin: 8px 0;
        }

        .preview-variables {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .variable-tag {
            background: var(--accent-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .scenario-badge {
            background: var(--success-color);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .game-name {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #138496, #117a8b);
            transform: scale(1.05);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-cog"></i> VOD Archive Configuration
                </h1>

                <div class="save-indicator">
                    <div id="saveStatus" class="alert alert-info d-none">
                        <i class="fas fa-spinner fa-spin"></i> Saving...
                    </div>
                </div>

                <!-- PRIMARY SECTIONS - Most Important -->

                <!-- Restricted Games -->
                <div class="config-section primary">
                    <h3 class="section-title primary">
                        <i class="fas fa-ban"></i> Restricted Games
                        <span class="priority-badge">PRIORITY</span>
                    </h3>
                    <p class="text-secondary mb-3">Games that will be skipped during YouTube upload processing</p>
                    <div id="restrictedGamesContainer"></div>
                    <button type="button" class="btn-add" onclick="addRestrictedGame()">
                        <i class="fas fa-plus"></i> Add Restricted Game
                    </button>
                </div>

                <!-- Playlists by Chapter -->
                <div class="config-section primary">
                    <h3 class="section-title primary">
                        <i class="fas fa-list"></i> Playlists by Chapter
                        <span class="priority-badge">PRIORITY</span>
                    </h3>
                    <p class="text-secondary mb-3">Map game/chapter names to YouTube playlist IDs</p>
                    <div id="playlistsContainer"></div>
                    <button type="button" class="btn-add" onclick="addPlaylist()">
                        <i class="fas fa-plus"></i> Add Playlist Mapping
                    </button>
                </div>

                <!-- Title Templates -->
                <div class="config-section primary">
                    <h3 class="section-title primary">
                        <i class="fas fa-edit"></i> Title Templates
                        <span class="priority-badge">PRIORITY</span>
                    </h3>
                    <p class="text-secondary mb-3">Custom title templates for different games. Use variables: ${config.channel}, ${gameName}, ${date}, ${episodeCount}, ${partNumber}</p>
                    <div id="titleTemplatesContainer"></div>
                    <div class="d-flex gap-2 mt-3">
                        <button type="button" class="btn-add" onclick="addTitleTemplate()">
                            <i class="fas fa-plus"></i> Add Title Template
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewTitles()">
                            <i class="fas fa-eye"></i> Preview Titles
                        </button>
                    </div>
                </div>

                <!-- SECONDARY SECTIONS - Less Frequently Changed -->

                <!-- General Settings (Collapsible) -->
                <div class="collapse-toggle" data-bs-toggle="collapse" data-bs-target="#generalSettings" aria-expanded="false">
                    <span><i class="fas fa-user"></i> General Settings</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="collapse" id="generalSettings">
                    <div class="config-section">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="channel" class="form-label">Channel Name</label>
                                <input type="text" class="form-control" id="channel" name="channel">
                            </div>
                            <div class="col-md-6">
                                <label for="timezone" class="form-label">Timezone</label>
                                <input type="text" class="form-control" id="timezone" name="timezone">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Platform Settings (Collapsible) -->
                <div class="collapse-toggle" data-bs-toggle="collapse" data-bs-target="#platformSettings" aria-expanded="false">
                    <span><i class="fas fa-broadcast-tower"></i> Platform Settings</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="collapse" id="platformSettings">
                    <div class="config-section">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-info">Twitch</h5>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="twitchEnabled" name="twitch.enabled">
                                    <label class="form-check-label" for="twitchEnabled">
                                        Enabled
                                    </label>
                                </div>
                                <label for="twitchUsername" class="form-label">Username</label>
                                <input type="text" class="form-control" id="twitchUsername" name="twitch.username">
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-success">Kick</h5>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="kickEnabled" name="kick.enabled">
                                    <label class="form-check-label" for="kickEnabled">
                                        Enabled
                                    </label>
                                </div>
                                <label for="kickUsername" class="form-label">Username</label>
                                <input type="text" class="form-control" id="kickUsername" name="kick.username">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Download Settings (Collapsible) -->
                <div class="collapse-toggle" data-bs-toggle="collapse" data-bs-target="#downloadSettings" aria-expanded="false">
                    <span><i class="fas fa-download"></i> Download Settings</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="collapse" id="downloadSettings">
                    <div class="config-section">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="chatDownload" name="chatDownload">
                                    <label class="form-check-label" for="chatDownload">
                                        Download Chat
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="vodDownload" name="vodDownload">
                                    <label class="form-check-label" for="vodDownload">
                                        Download VODs
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="saveHLS" name="saveHLS">
                                    <label class="form-check-label" for="saveHLS">
                                        Save HLS
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="saveMP4" name="saveMP4">
                                    <label class="form-check-label" for="saveMP4">
                                        Save MP4
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- YouTube Settings (Collapsible) -->
                <div class="collapse-toggle" data-bs-toggle="collapse" data-bs-target="#youtubeSettings" aria-expanded="false">
                    <span><i class="fab fa-youtube"></i> YouTube Settings</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="collapse" id="youtubeSettings">
                    <div class="config-section">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="youtubeUpload" name="youtube.upload">
                                    <label class="form-check-label" for="youtubeUpload">
                                        Enable Upload
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="youtubePublic" name="youtube.public">
                                    <label class="form-check-label" for="youtubePublic">
                                        Public Videos
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="youtubeVodUpload" name="youtube.vodUpload">
                                    <label class="form-check-label" for="youtubeVodUpload">
                                        VOD Upload
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="youtubePerGameUpload" name="youtube.perGameUpload">
                                    <label class="form-check-label" for="youtubePerGameUpload">
                                        Per Game Upload
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="youtubeLiveUpload" name="youtube.liveUpload">
                                    <label class="form-check-label" for="youtubeLiveUpload">
                                        Live Upload
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="youtubeMultiTrack" name="youtube.multiTrack">
                                    <label class="form-check-label" for="youtubeMultiTrack">
                                        Multi Track
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="youtubeSplitDuration" class="form-label">Split Duration (seconds)</label>
                                <input type="number" class="form-control" id="youtubeSplitDuration" name="youtube.splitDuration">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="youtubeDescription" class="form-label">Description Template</label>
                            <textarea class="form-control" id="youtubeDescription" name="youtube.description" rows="4"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="text-center">
                    <button type="button" class="btn btn-primary btn-lg" onclick="saveConfig()">
                        <i class="fas fa-save"></i> Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Title Preview Modal -->
    <div class="modal fade" id="titlePreviewModal" tabindex="-1" aria-labelledby="titlePreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content" style="background: var(--bg-secondary); border: 1px solid var(--border-color);">
                <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title" id="titlePreviewModalLabel" style="color: var(--text-primary);">
                        <i class="fas fa-eye"></i> Title Template Previews
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <div id="previewContent">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x" style="color: var(--accent-color);"></i>
                            <p class="mt-2" style="color: var(--text-secondary);">Loading previews...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="refreshPreviews()">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="config.js"></script>
</body>
</html>
