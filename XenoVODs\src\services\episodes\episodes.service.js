// Initializes the `episodes` service on path `/episodes`
const { Episodes } = require('./episodes.class');
const createModel = require('../../models/episodes.model');
const hooks = require('./episodes.hooks');

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/episodes', new Episodes(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('episodes');

  service.hooks(hooks);
};